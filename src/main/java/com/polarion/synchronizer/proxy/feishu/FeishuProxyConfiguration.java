package com.polarion.synchronizer.proxy.feishu;

import com.polarion.synchronizer.configuration.FieldMappingGroupConfiguration;
import com.polarion.synchronizer.configuration.IConnection;
import com.polarion.synchronizer.configuration.MappingConfiguration;
import com.polarion.synchronizer.spi.AbstractProxyConfiguration;
import org.jetbrains.annotations.Nullable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

/**
 * 飞书项目代理配置类
 * 定义同步配置参数
 */
@XmlRootElement(name = "feishuProxyConfiguration")
@XmlAccessorType(XmlAccessType.FIELD)
public class FeishuProxyConfiguration extends AbstractProxyConfiguration<FeishuConnection> {
    
    private static final String SYSTEM_ID_PREFIX = "FEISHU-";
    
    @XmlAttribute
    private String projectKey;
    
    @XmlAttribute
    private String workItemTypes;
    
    @XmlAttribute
    private String query;
    
    @XmlAttribute
    private String spaceId;

    @XmlElement
    private MappingConfiguration mappingConfiguration;

    @XmlTransient
    private int pageSize = 100;

    @XmlTransient
    private int maxRetries = 3;

    @XmlTransient
    private FeishuFieldMapping fieldMapping;
    
    public FeishuProxyConfiguration() {
    }
    
    public FeishuProxyConfiguration(String projectKey, String workItemTypes, FeishuConnection connection) {
        this.projectKey = projectKey;
        this.workItemTypes = workItemTypes;
        this.setConnection(connection);
        // 初始化默认映射配置
        this.mappingConfiguration = createDefaultMappingConfiguration();
    }
    
    public String getProjectKey() {
        return projectKey;
    }
    
    public void setProjectKey(String projectKey) {
        this.projectKey = projectKey;
    }
    
    public String getWorkItemTypes() {
        return workItemTypes;
    }
    
    public void setWorkItemTypes(String workItemTypes) {
        this.workItemTypes = workItemTypes;
    }
    
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
    
    public String getSpaceId() {
        return spaceId;
    }
    
    public void setSpaceId(String spaceId) {
        this.spaceId = spaceId;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public MappingConfiguration getMappingConfiguration() {
        if (mappingConfiguration == null) {
            mappingConfiguration = createDefaultMappingConfiguration();
        }
        return mappingConfiguration;
    }

    public void setMappingConfiguration(MappingConfiguration mappingConfiguration) {
        this.mappingConfiguration = mappingConfiguration;
        // 重新初始化字段映射
        this.fieldMapping = null;
    }

    /**
     * 获取字段映射配置
     * @return 字段映射配置对象
     */
    @XmlTransient
    public FeishuFieldMapping getFieldMapping() {
        if (fieldMapping == null) {
            fieldMapping = new FeishuFieldMapping();
            // 从标准映射配置加载字段映射
            loadFieldMappingFromConfiguration();
        }
        return fieldMapping;
    }

    /**
     * 创建默认映射配置
     * @return 默认映射配置
     */
    private MappingConfiguration createDefaultMappingConfiguration() {
        FieldMappingGroupConfiguration defaultGroup = new FieldMappingGroupConfiguration("WorkItem", "FeishuWorkItem");
        return new MappingConfiguration(defaultGroup);
    }

    /**
     * 从标准映射配置加载字段映射
     */
    private void loadFieldMappingFromConfiguration() {
        if (mappingConfiguration != null && mappingConfiguration.getDefaultMappingGroup() != null) {
            // 这里可以根据需要从MappingConfiguration加载到FeishuFieldMapping
            // 暂时保持现有的默认映射
        }
    }
    
    /**
     * 获取飞书连接配置
     * @return 飞书连接配置
     */
    @Nullable
    private FeishuConnection getFeishuConnection() {
        IConnection connection = this.getConnection();
        if (connection == null) {
            return null;
        } else if (connection instanceof FeishuConnection) {
            return (FeishuConnection) connection;
        } else {
            throw new IllegalStateException("Connection is not a FeishuConnection.");
        }
    }
    
    @XmlTransient
    public String getServerUrl() {
        FeishuConnection feishuConnection = this.getFeishuConnection();
        return feishuConnection == null ? null : feishuConnection.getServerUrl();
    }

    @XmlTransient
    public String getPluginToken() {
        FeishuConnection feishuConnection = this.getFeishuConnection();
        return feishuConnection == null ? null : feishuConnection.getPluginToken();
    }

    @XmlTransient
    public String getUserKey() {
        FeishuConnection feishuConnection = this.getFeishuConnection();
        return feishuConnection == null ? null : feishuConnection.getUserKey();
    }
    
    /**
     * 获取系统标识符
     * @return 系统标识符
     */
    public String getSystemIdentifier() {
        FeishuConnection feishuConnection = this.getFeishuConnection();
        return SYSTEM_ID_PREFIX + (feishuConnection == null ? null : feishuConnection.getId());
    }
    
    /**
     * 检查配置的有效性
     * @return 错误信息，如果配置有效则返回null
     */
    @Nullable
    public String checkConfiguration() {
        FeishuConnection connection = this.getFeishuConnection();
        if (connection == null) {
            return "未配置飞书项目连接";
        }
        
        String connectionError = connection.check();
        if (connectionError != null) {
            return connectionError;
        }
        
        if (projectKey == null || projectKey.trim().isEmpty()) {
            return "项目Key不能为空";
        }
        
        return null;
    }
    
    /**
     * 获取API基础URL
     * @return API基础URL
     */
    @XmlTransient
    public String getApiBaseUrl() {
        FeishuConnection feishuConnection = this.getFeishuConnection();
        return feishuConnection == null ? null : feishuConnection.getApiBaseUrl();
    }

    /**
     * 构建工作项查询URL（使用filter接口）
     * @return 工作项查询URL
     */
    @XmlTransient
    public String getWorkItemQueryUrl() {
        if (getApiBaseUrl() == null || projectKey == null) {
            return null;
        }
        return getApiBaseUrl() + "/" + projectKey + "/work_item/filter";
    }

    /**
     * 构建工作项创建URL
     * @return 工作项创建URL
     */
    @XmlTransient
    public String getWorkItemCreateUrl() {
        if (getApiBaseUrl() == null || projectKey == null) {
            return null;
        }
        return getApiBaseUrl() + "/" + projectKey + "/work_item/create";
    }

    /**
     * 构建工作项更新URL
     * @param workItemId 工作项ID
     * @param workItemTypeKey 工作项类型
     * @return 工作项更新URL
     */
    @XmlTransient
    public String getWorkItemUpdateUrl(String workItemId, String workItemTypeKey) {
        if (getApiBaseUrl() == null || projectKey == null || workItemId == null || workItemTypeKey == null) {
            return null;
        }
        return getApiBaseUrl() + "/" + projectKey + "/work_item/" + workItemTypeKey + "/" + workItemId;
    }
}
