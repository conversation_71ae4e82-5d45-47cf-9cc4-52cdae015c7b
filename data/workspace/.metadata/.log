!SESSION 2025-07-24 18:15:47.068 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY com.polarion.platform.jobs 4 0 2025-07-24 18:16:00.293
!MESSAGE See logs for details.
!SESSION 2025-07-27 19:24:33.660 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 4 0 2025-07-27 19:24:51.994
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.plugin.manage (10).
!STACK 0
org.osgi.framework.BundleException: Exception in com.fasnote.alm.plugin.manage.Activator.start() of bundle com.fasnote.alm.plugin.manage.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/injection/module/LicenseModule
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:95)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(Unknown Source)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 48 more
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.injection.module.LicenseModule cannot be found by com.fasnote.alm.plugin.manage_2.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 56 more
Root exception:
java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/injection/module/LicenseModule
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:95)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(Unknown Source)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.injection.module.LicenseModule cannot be found by com.fasnote.alm.plugin.manage_2.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 56 more
!SESSION 2025-07-28 17:51:36.271 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 17:51:49.045
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-6,1,main]" timed out waiting (5001ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-6,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-28 18:10:03.086 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 4 0 2025-07-28 18:10:11.169
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.auth.feishu (7).
!STACK 0
org.osgi.framework.BundleException: Exception in com.fasnote.alm.auth.feishu.Activator.start() of bundle com.fasnote.alm.auth.feishu.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/api/IFallbackModule
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 36 more
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.api.IFallbackModule cannot be found by com.fasnote.alm.auth.feishu_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 56 more
Root exception:
java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/api/IFallbackModule
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.api.IFallbackModule cannot be found by com.fasnote.alm.auth.feishu_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 56 more
!SESSION 2025-07-28 18:12:21.658 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 18:12:37.386
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-2,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-3,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-2,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 4 0 2025-07-28 18:14:27.596
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.plugin.manage (9).
!STACK 0
org.osgi.framework.BundleException: Exception in com.fasnote.alm.plugin.manage.Activator.start() of bundle com.fasnote.alm.plugin.manage.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: org/nuvola/indexeddb/client/ConnectionCallback
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.loadClass(EquinoxBundle.java:609)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanBundleClasses(DevelopmentAnnotationScanner.java:150)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanClasses(DevelopmentAnnotationScanner.java:117)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanLicenseImplementations(DevelopmentAnnotationScanner.java:45)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.scanDevelopmentImplementations(LicenseManager.java:1429)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.tryCreateDevelopmentImplementation(LicenseManager.java:1385)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:856)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:489)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:980)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:829)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 57 more
Caused by: java.lang.ClassNotFoundException: org.nuvola.indexeddb.client.ConnectionCallback cannot be found by com.polarion.alm.ui_3.22.1
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 94 more
Root exception:
java.lang.NoClassDefFoundError: org/nuvola/indexeddb/client/ConnectionCallback
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.loadClass(EquinoxBundle.java:609)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanBundleClasses(DevelopmentAnnotationScanner.java:150)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanClasses(DevelopmentAnnotationScanner.java:117)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanLicenseImplementations(DevelopmentAnnotationScanner.java:45)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.scanDevelopmentImplementations(LicenseManager.java:1429)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.tryCreateDevelopmentImplementation(LicenseManager.java:1385)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:856)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:489)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:980)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:829)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: org.nuvola.indexeddb.client.ConnectionCallback cannot be found by com.polarion.alm.ui_3.22.1
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 94 more

!ENTRY org.eclipse.osgi 4 0 2025-07-28 18:14:27.598
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.auth.feishu (7).
!STACK 0
org.osgi.framework.BundleException: Exception in com.fasnote.alm.auth.feishu.Activator.start() of bundle com.fasnote.alm.auth.feishu.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: com/fasnote/alm/auth/feishu/injection/FeishuFallbackModule
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 36 more
Caused by: java.lang.ClassNotFoundException: An error occurred while automatically activating bundle com.fasnote.alm.plugin.manage (9).
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:123)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 42 more
Caused by: org.osgi.framework.BundleException: Exception in com.fasnote.alm.plugin.manage.Activator.start() of bundle com.fasnote.alm.plugin.manage.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	... 50 more
Caused by: java.lang.NoClassDefFoundError: org/nuvola/indexeddb/client/ConnectionCallback
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.loadClass(EquinoxBundle.java:609)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanBundleClasses(DevelopmentAnnotationScanner.java:150)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanClasses(DevelopmentAnnotationScanner.java:117)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanLicenseImplementations(DevelopmentAnnotationScanner.java:45)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.scanDevelopmentImplementations(LicenseManager.java:1429)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.tryCreateDevelopmentImplementation(LicenseManager.java:1385)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:856)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:489)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:980)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:829)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 57 more
Caused by: java.lang.ClassNotFoundException: org.nuvola.indexeddb.client.ConnectionCallback cannot be found by com.polarion.alm.ui_3.22.1
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 94 more
Root exception:
java.lang.NoClassDefFoundError: com/fasnote/alm/auth/feishu/injection/FeishuFallbackModule
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:60)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: An error occurred while automatically activating bundle com.fasnote.alm.plugin.manage (9).
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:123)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 42 more
Caused by: org.osgi.framework.BundleException: Exception in com.fasnote.alm.plugin.manage.Activator.start() of bundle com.fasnote.alm.plugin.manage.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	... 50 more
Caused by: java.lang.NoClassDefFoundError: org/nuvola/indexeddb/client/ConnectionCallback
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(ModuleClassLoader.java:276)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(ClasspathManager.java:632)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(ClasspathManager.java:555)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(ClasspathManager.java:514)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:501)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.loadClass(EquinoxBundle.java:609)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanBundleClasses(DevelopmentAnnotationScanner.java:150)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanClasses(DevelopmentAnnotationScanner.java:117)
	at com.fasnote.alm.plugin.manage.util.DevelopmentAnnotationScanner.scanLicenseImplementations(DevelopmentAnnotationScanner.java:45)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.scanDevelopmentImplementations(LicenseManager.java:1429)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.tryCreateDevelopmentImplementation(LicenseManager.java:1385)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:856)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:489)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:980)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:829)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 57 more
Caused by: java.lang.ClassNotFoundException: org.nuvola.indexeddb.client.ConnectionCallback cannot be found by com.polarion.alm.ui_3.22.1
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 94 more
!SESSION 2025-07-28 20:23:36.011 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:23:49.646
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-5,1,main]" timed out waiting (5004ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-5,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-28 20:28:31.977 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:28:45.280
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-4,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-3,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-4,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-28 20:53:22.120 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:53:35.435
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-5,1,main]" timed out waiting (5002ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-5,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-28 20:54:06.658 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:54:19.740
!MESSAGE While loading class "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer", thread "Thread[Catalina-utility-5,1,main]" timed out waiting (5005ms) for thread "Thread[Catalina-utility-1,1,main]" to finish starting bundle "com.fasnote.alm.auth.feishu_1.0.0.qualifier [7]". To avoid deadlock, thread "Thread[Catalina-utility-5,1,main]" is proceeding but "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.auth.feishu"; type="osgi.bundle"; version:Version="1.0.0.qualifier"; singleton:="true" [id=7] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.loadInterfaceClass(LicenseModule.java:294)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:247)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 74 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.loadInterfaceClass(LicenseModule.java:294)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:247)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:54:24.748
!MESSAGE While loading class "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer", thread "Thread[Catalina-utility-5,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-1,1,main]" to finish starting bundle "com.fasnote.alm.auth.feishu_1.0.0.qualifier [7]". To avoid deadlock, thread "Thread[Catalina-utility-5,1,main]" is proceeding but "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.auth.feishu"; type="osgi.bundle"; version:Version="1.0.0.qualifier"; singleton:="true" [id=7] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:493)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:984)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:833)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 77 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:733)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:493)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:984)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:833)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:54:39.155
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5013ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 53 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:03.061
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrap", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5007ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrap" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:50)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 54 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:50)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:08.168
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.facade.LicenseManagementFacade", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5001ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.facade.LicenseManagementFacade" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:59)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 54 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:59)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:13.197
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.facade.ConfigurationFacade", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5005ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.facade.ConfigurationFacade" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:60)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 54 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:60)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:18.232
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.facade.SecurityFacade", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.facade.SecurityFacade" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:61)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 54 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:61)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:23.275
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5005ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:62)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 54 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.<init>(LicenseFrameworkFacade.java:62)
	at com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade.getInstance(LicenseFrameworkFacade.java:72)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:29)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:55:35.532
!MESSAGE While loading class "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer", thread "Thread[Catalina-utility-5,1,main]" timed out waiting (5011ms) for thread "Thread[Catalina-utility-1,1,main]" to finish starting bundle "com.fasnote.alm.auth.feishu_1.0.0.qualifier [7]". To avoid deadlock, thread "Thread[Catalina-utility-5,1,main]" is proceeding but "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.auth.feishu"; type="osgi.bundle"; version:Version="1.0.0.qualifier"; singleton:="true" [id=7] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.findClass(EncryptedClassLoader.java:166)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:208)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:888)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:737)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:493)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:984)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:833)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 83 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:581)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:219)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1022)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.findClass(EncryptedClassLoader.java:166)
	at com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader.loadClass(EncryptedClassLoader.java:208)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createImplementationInstanceFromLicense(LicenseManager.java:888)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.loadAndRegisterImplementations(LicenseManager.java:737)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.setupRuntimeEnvironment(LicenseManager.java:493)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.ensureRuntimeEnvironmentInitialized(LicenseManager.java:984)
	at com.fasnote.alm.plugin.manage.core.LicenseManager.createServiceInstanceFromLicense(LicenseManager.java:833)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseService(LicenseModule.java:254)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.registerLicenseServices(LicenseModule.java:219)
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:58)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:56:27.753
!MESSAGE While loading class "com.fasnote.alm.auth.feishu.injection.FeishuFallbackModule", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.auth.feishu.injection.FeishuFallbackModule" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:62)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 53 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:62)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:56:32.837
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.api.IFallbackModule", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5003ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.api.IFallbackModule" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.checkServiceClass(ServiceRegistry.java:1113)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:215)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:469)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:487)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:1004)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:65)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 59 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.checkServiceClass(ServiceRegistry.java:1113)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:215)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:469)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:487)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:1004)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:65)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)

!ENTRY org.eclipse.osgi 2 0 2025-07-28 20:56:37.933
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.api.IFallbackBinder", thread "Thread[Catalina-utility-1,1,main]" timed out waiting (5006ms) for thread "Thread[Catalina-utility-5,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-1,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.api.IFallbackBinder" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.injection.FeishuFallbackModule.configureFallbacks(FeishuFallbackModule.java:24)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager.addFallbackModule(FallbackModuleManager.java:199)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager$1.addingService(FallbackModuleManager.java:111)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager$1.addingService(FallbackModuleManager.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.ServiceTracker$Tracked.serviceChanged(ServiceTracker.java:903)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:109)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:920)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:862)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:801)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:127)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:225)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:469)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:487)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:1004)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:65)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 74 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(SingleSourcePackage.java:36)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:466)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.fasnote.alm.auth.feishu.injection.FeishuFallbackModule.configureFallbacks(FeishuFallbackModule.java:24)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager.addFallbackModule(FallbackModuleManager.java:199)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager$1.addingService(FallbackModuleManager.java:111)
	at com.fasnote.alm.plugin.manage.injection.FallbackModuleManager$1.addingService(FallbackModuleManager.java:1)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:943)
	at org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(ServiceTracker.java:1)
	at org.osgi.util.tracker.AbstractTracked.trackAdding(AbstractTracked.java:256)
	at org.osgi.util.tracker.AbstractTracked.track(AbstractTracked.java:229)
	at org.osgi.util.tracker.ServiceTracker$Tracked.serviceChanged(ServiceTracker.java:903)
	at org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(FilteredServiceListener.java:109)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(BundleContextImpl.java:920)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(ServiceRegistry.java:862)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(ServiceRegistry.java:801)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(ServiceRegistrationImpl.java:127)
	at org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(ServiceRegistry.java:225)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:469)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:487)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(BundleContextImpl.java:1004)
	at com.fasnote.alm.auth.feishu.Activator.registerFallbackModuleService(Activator.java:65)
	at com.fasnote.alm.auth.feishu.Activator.start(Activator.java:31)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-29 11:36:14.729 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 4 0 2025-07-29 11:43:51.896
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.plugin.manage (9).
!STACK 0
org.osgi.framework.BundleException: Exception in com.fasnote.alm.plugin.manage.Activator.start() of bundle com.fasnote.alm.plugin.manage.
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:800)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/injection/module/LicenseModule$LicenseServiceInterceptor
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:68)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	... 48 more
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseServiceInterceptor cannot be found by com.fasnote.alm.plugin.manage_2.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 58 more
Root exception:
java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/injection/module/LicenseModule$LicenseServiceInterceptor
	at com.fasnote.alm.plugin.manage.injection.module.LicenseModule.configure(LicenseModule.java:68)
	at com.fasnote.alm.injection.impl.DependencyInjector.installModule(DependencyInjector.java:513)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.installLicenseModule(OSGiLicenseFrameworkInitializer.java:106)
	at com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer.initialize(OSGiLicenseFrameworkInitializer.java:53)
	at com.fasnote.alm.plugin.manage.Activator.initializeOSGiLicenseFramework(Activator.java:241)
	at com.fasnote.alm.plugin.manage.Activator.start(Activator.java:92)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:779)
	at org.eclipse.osgi.internal.framework.BundleContextImpl$3.run(BundleContextImpl.java:1)
	at java.base/java.security.AccessController.doPrivileged(Native Method)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(BundleContextImpl.java:772)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:729)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseServiceInterceptor cannot be found by com.fasnote.alm.plugin.manage_2.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 58 more
!SESSION 2025-07-29 13:15:22.225 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 4 0 2025-07-29 13:15:31.268
!MESSAGE An error occurred while automatically activating bundle com.fasnote.alm.auth.feishu (7).
!STACK 0
org.osgi.framework.BundleException: Error starting module.
	at org.eclipse.osgi.container.Module.doStart(Module.java:590)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/api/IPackageScanProvider
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3137)
	at java.base/java.lang.Class.getConstructor0(Class.java:3342)
	at java.base/java.lang.Class.newInstance(Class.java:556)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.loadBundleActivator(BundleContextImpl.java:763)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:716)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	... 32 more
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.api.IPackageScanProvider cannot be found by com.fasnote.alm.auth.feishu_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 41 more
Root exception:
java.lang.NoClassDefFoundError: com/fasnote/alm/plugin/manage/api/IPackageScanProvider
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3137)
	at java.base/java.lang.Class.getConstructor0(Class.java:3342)
	at java.base/java.lang.Class.newInstance(Class.java:556)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.loadBundleActivator(BundleContextImpl.java:763)
	at org.eclipse.osgi.internal.framework.BundleContextImpl.start(BundleContextImpl.java:716)
	at org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0(EquinoxBundle.java:1002)
	at org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker(EquinoxBundle.java:354)
	at org.eclipse.osgi.container.Module.doStart(Module.java:581)
	at org.eclipse.osgi.container.Module.start(Module.java:449)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538)
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519)
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.lang.ClassNotFoundException: com.fasnote.alm.plugin.manage.api.IPackageScanProvider cannot be found by com.fasnote.alm.auth.feishu_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	... 41 more
!SESSION 2025-07-29 13:59:18.186 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-29 13:59:35.409
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-6,1,main]" timed out waiting (5002ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-6,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-29 14:06:59.531 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-29 14:07:12.913
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-3,1,main]" timed out waiting (5005ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-3,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-29 14:09:45.301 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-29 14:09:58.501
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-4,1,main]" timed out waiting (5005ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-4,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-29 14:11:58.735 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-29 14:12:11.894
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-2,1,main]" timed out waiting (5007ms) for thread "Thread[Catalina-utility-1,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-2,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
!SESSION 2025-07-29 18:03:17.680 -----------------------------------------------
eclipse.buildId=unknown
java.version=11.0.27
java.vendor=Microsoft
BootLoader constants: OS=linux, ARCH=arm64, WS=linux, NL=zh_CN_#Hans
Framework arguments:  -application com.polarion.core.boot.app -appId polarion.server
Command-line arguments:  -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server

!ENTRY org.eclipse.osgi 2 0 2025-07-29 18:03:33.158
!MESSAGE While loading class "com.fasnote.alm.plugin.manage.web.config.WebConfig", thread "Thread[Catalina-utility-4,1,main]" timed out waiting (5004ms) for thread "Thread[Catalina-utility-2,1,main]" to finish starting bundle "com.fasnote.alm.plugin.manage_2.0.0.qualifier [9]". To avoid deadlock, thread "Thread[Catalina-utility-4,1,main]" is proceeding but "com.fasnote.alm.plugin.manage.web.config.WebConfig" may not be fully initialized.
!STACK 0
org.osgi.framework.BundleException: Unable to acquire the state change lock for the module: osgi.identity; osgi.identity="com.fasnote.alm.plugin.manage"; type="osgi.bundle"; version:Version="2.0.0.qualifier"; singleton:="true" [id=9] STARTED [STARTED]
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:337)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	... 44 more
Root exception:
java.util.concurrent.TimeoutException: Timeout after waiting 5 seconds to acquire the lock.
	at org.eclipse.osgi.container.Module.lockStateChange(Module.java:334)
	at org.eclipse.osgi.container.Module.start(Module.java:401)
	at org.eclipse.osgi.framework.util.SecureAction.start(SecureAction.java:468)
	at org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(EclipseLazyStarter.java:114)
	at org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(ClasspathManager.java:505)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.findLocalClass(ModuleClassLoader.java:328)
	at org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(BundleLoader.java:392)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:470)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419)
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411)
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:527)
	at com.polarion.portal.tomcat.WebAppClassLoader.loadClass(WebAppClassLoader.java:35)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1351)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:398)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:284)
	at org.springframework.web.context.support.AnnotationConfigWebApplicationContext.loadBeanDefinitions(AnnotationConfigWebApplicationContext.java:235)
	at org.springframework.context.support.AbstractRefreshableApplicationContext.refreshBeanFactory(AbstractRefreshableApplicationContext.java:130)
	at org.springframework.context.support.AbstractApplicationContext.obtainFreshBeanFactory(AbstractApplicationContext.java:638)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:523)
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668)
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143)
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096)
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989)
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
