/**
 * 飞书项目连接器UI组件
 * 定义前端配置界面的JavaScript组件
 */

// 飞书项目代理配置模型
App.FeishuProxyConfiguration = App.ProxyConfiguration.extend({
    resourceProperties: [
        'proxyType',
        'projectKey',
        'workItemTypes',
        'query',
        'spaceId',
        'mappingConfiguration',
        'connection'
    ],
    type: "",
    description: function() {
        return this.connection == null ? 
                '未配置连接' :
                this.projectKey == null ? 
                        '未指定项目Key，服务器: ' + this.connection.serverUrl :
                        '项目: ' + this.projectKey + '，服务器: ' + this.connection.serverUrl;
    }.property('projectKey', 'connection'),
    name: '飞书项目配置'
});

// 飞书项目代理配置视图
App.FeishuProxyConfigurationView = Ember.View.extend({
    templateName: 'feishuProxyConfiguration',
    classNames: ["rows"]
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({});

// 飞书项目连接模型
App.FeishuConnection = App.Connection.extend({
    resourceProperties: [
        'id',
        'connectionType',
        'serverUrl',
        'pluginToken',
        'userKey'
    ],
    description: function() {
        return '飞书项目连接: ' + this.serverUrl;
    }.property('serverUrl')
});

// 飞书项目连接视图
App.FeishuConnectionView = Ember.View.extend({
    templateName: 'feishuConnection',
    classNames: ['rows']
});

// 飞书项目代理配置控制器
App.FeishuProxyConfigurationController = Ember.ObjectController.extend({
    // 可以在这里添加自定义的控制器逻辑

    // 验证项目Key格式
    validateProjectKey: function() {
        var projectKey = this.get('projectKey');
        if (projectKey && projectKey.trim().length > 0) {
            // 可以添加项目Key格式验证逻辑
            return true;
        }
        return false;
    },

    // 验证字段映射配置格式
    validateFieldMapping: function() {
        var fieldMappingConfig = this.get('fieldMappingConfig');
        if (!fieldMappingConfig || fieldMappingConfig.trim().length === 0) {
            return true; // 空配置是允许的
        }

        try {
            var mappings = fieldMappingConfig.split(',');
            for (var i = 0; i < mappings.length; i++) {
                var mapping = mappings[i].trim();
                var parts = mapping.split(':');
                if (parts.length < 2) {
                    return false;
                }
            }
            return true;
        } catch (e) {
            return false;
        }
    },

    // 获取工作项类型选项
    workItemTypeOptions: [
        { value: 'story', label: 'Story' },
        { value: 'task', label: 'Task' },
        { value: 'bug', label: 'Bug' },
        { value: 'epic', label: 'Epic' }
    ],

    // 获取字段类型选项
    fieldTypeOptions: [
        { value: 'TEXT', label: '文本' },
        { value: 'NUMBER', label: '数字' },
        { value: 'OPTION', label: '选项' },
        { value: 'USER', label: '用户' },
        { value: 'DATE', label: '日期' },
        { value: 'MULTI_OPTION', label: '多选' },
        { value: 'RICH_TEXT', label: '富文本' }
    ]
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({
    // 测试连接功能
    testConnection: function() {
        var connection = this.get('content');
        if (!connection.serverUrl || !connection.pluginToken) {
            alert('请填写服务器地址和插件Token');
            return;
        }

        // 这里可以添加连接测试逻辑
        // 实际实现中可能需要调用后端API进行连接测试
        alert('连接测试功能待实现');
    }
});
